<?php
define('SKIP_ROUTE', TRUE);
include './index.php';

echo "<h2>管理后台函数测试</h2>\n";

echo "<h3>1. 基础函数检查</h3>\n";
echo "user_is_admin 函数存在: " . (function_exists('user_is_admin') ? '✅' : '❌') . "<br>\n";
echo "admin_token_check 函数存在: " . (function_exists('admin_token_check') ? '✅' : '❌') . "<br>\n";

if (function_exists('user_is_admin')) {
    echo "user_is_admin(1): " . (user_is_admin(1) ? '✅ true' : '❌ false') . "<br>\n";
    echo "user_is_admin(2): " . (user_is_admin(2) ? '✅ true' : '❌ false') . "<br>\n";
    echo "user_is_admin(3): " . (user_is_admin(3) ? '✅ true' : '❌ false') . "<br>\n";
}

echo "<h3>2. 加载管理后台函数</h3>\n";
try {
    include _include(ADMIN_PATH."admin.func.php");
    echo "✅ admin.func.php 加载成功<br>\n";
    echo "admin_token_check 函数存在: " . (function_exists('admin_token_check') ? '✅' : '❌') . "<br>\n";
} catch (Exception $e) {
    echo "❌ admin.func.php 加载失败: " . $e->getMessage() . "<br>\n";
}

echo "<h3>3. 当前用户信息</h3>\n";
echo "当前用户 gid: " . (isset($gid) ? $gid : 'undefined') . "<br>\n";
echo "当前用户 uid: " . (isset($uid) ? $uid : 'undefined') . "<br>\n";

if (isset($user) && !empty($user)) {
    echo "用户名: " . htmlspecialchars($user['username']) . "<br>\n";
    echo "用户组: " . $user['gid'] . "<br>\n";
    if (function_exists('user_is_admin')) {
        echo "是否管理员: " . (user_is_admin($user['gid']) ? '✅ 是' : '❌ 否') . "<br>\n";
    }
} else {
    echo "用户未登录<br>\n";
}

echo "<h3>4. 测试管理员登录</h3>\n";
echo "请使用以下信息登录：<br>\n";
echo "用户名: admin<br>\n";
echo "密码: 1<br>\n";
echo "<a href='?user-login.htm'>点击登录</a><br>\n";
?>
