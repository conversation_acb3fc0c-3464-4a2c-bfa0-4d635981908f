<?php
define('SKIP_ROUTE', TRUE);
include './index.php';

echo "<h2>用户函数调试</h2>\n";

echo "<h3>1. 直接包含用户函数文件</h3>\n";
try {
    include APP_PATH.'model/user.func.php';
    echo "✅ user.func.php 包含成功<br>\n";
} catch (Exception $e) {
    echo "❌ user.func.php 包含失败: " . $e->getMessage() . "<br>\n";
}

echo "<h3>2. 检查函数定义</h3>\n";
$functions_to_check = array(
    'user_is_admin',
    'user_is_moderator', 
    'user_has_permission',
    'user_can_access_forum',
    'user_status_check',
    'is_password_strong'
);

foreach ($functions_to_check as $func) {
    echo "$func: " . (function_exists($func) ? '✅ 存在' : '❌ 不存在') . "<br>\n";
}

echo "<h3>3. 测试函数调用</h3>\n";
if (function_exists('user_is_admin')) {
    echo "user_is_admin(1): " . (user_is_admin(1) ? '✅ true' : '❌ false') . "<br>\n";
    echo "user_is_admin(2): " . (user_is_admin(2) ? '✅ true' : '❌ false') . "<br>\n";
    echo "user_is_admin(3): " . (user_is_admin(3) ? '✅ true' : '❌ false') . "<br>\n";
}

echo "<h3>4. 检查文件内容</h3>\n";
$user_func_file = APP_PATH.'model/user.func.php';
if (is_file($user_func_file)) {
    $content = file_get_contents($user_func_file);
    $has_function = strpos($content, 'function user_is_admin') !== false;
    echo "文件存在: ✅<br>\n";
    echo "文件大小: " . number_format(strlen($content)) . " bytes<br>\n";
    echo "包含 user_is_admin 函数: " . ($has_function ? '✅' : '❌') . "<br>\n";
    
    if ($has_function) {
        $lines = explode("\n", $content);
        foreach ($lines as $i => $line) {
            if (strpos($line, 'function user_is_admin') !== false) {
                echo "函数定义在第 " . ($i + 1) . " 行<br>\n";
                break;
            }
        }
    }
} else {
    echo "❌ 文件不存在<br>\n";
}
?>
