-- 数据库性能检查脚本

USE xiuno_bbs;

-- 1. 显示数据库基本信息
SELECT 'Database Information' as Info;
SELECT 
    table_name as 'Table Name',
    table_rows as 'Rows',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as 'Size (MB)',
    ROUND((data_length / 1024 / 1024), 2) as 'Data (MB)',
    ROUND((index_length / 1024 / 1024), 2) as 'Index (MB)'
FROM information_schema.tables 
WHERE table_schema = 'xiuno_bbs'
ORDER BY (data_length + index_length) DESC;

-- 2. 显示索引使用情况
SELECT 'Index Usage' as Info;
SELECT 
    table_name as 'Table',
    index_name as 'Index',
    column_name as 'Column',
    cardinality as 'Cardinality'
FROM information_schema.statistics 
WHERE table_schema = 'xiuno_bbs'
ORDER BY table_name, index_name;

-- 3. 检查慢查询设置
SELECT 'Slow Query Settings' as Info;
SHOW VARIABLES LIKE 'slow_query%';
SHOW VARIABLES LIKE 'long_query_time';

-- 4. 显示当前连接状态
SELECT 'Connection Status' as Info;
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Threads_running';

-- 5. 显示查询缓存状态
SELECT 'Query Cache Status' as Info;
SHOW STATUS LIKE 'Qcache%';

-- 6. 显示 InnoDB 状态（如果使用 InnoDB）
SELECT 'InnoDB Status' as Info;
SHOW STATUS LIKE 'Innodb_buffer_pool%';

-- 7. 建议的配置优化
SELECT 'Optimization Suggestions' as Info;
SELECT 
    'Consider enabling slow query log' as Suggestion,
    'SET GLOBAL slow_query_log = ON;' as Command
UNION ALL
SELECT 
    'Consider setting long_query_time to 2 seconds',
    'SET GLOBAL long_query_time = 2;'
UNION ALL
SELECT 
    'Consider increasing key_buffer_size for MyISAM',
    'SET GLOBAL key_buffer_size = 128M;'
UNION ALL
SELECT 
    'Consider enabling query cache',
    'SET GLOBAL query_cache_type = ON;';

-- 8. 显示表状态
SELECT 'Table Status' as Info;
SHOW TABLE STATUS FROM xiuno_bbs;
