<?php include _include(APP_PATH.'view/htm/header.inc.htm');?>



<div class="row">
	<div class="col-lg-6 mx-auto">
		<div class="card">
			<div class="card-header">
				<?php echo lang('user_login');?>
				
			</div>
			<div class="card-body ajax_modal_body">
				<form action="<?php echo url('user-login');?>" method="post" id="form">
					<div class="form-group input-group">
						<div class="input-group-prepend">
							<span class="input-group-text"><i class="icon icon-user icon-fw"></i></span>
						</div>
						<input type="text" class="form-control" placeholder="<?php echo lang('email');?> / <?php echo lang('username');?>" id="email" name="email">
						<div class="invalid-feedback"></div>
					</div>
					
					<div class="form-group input-group">
						<div class="input-group-prepend">
							<span class="input-group-text"><i class="icon icon-lock icon-fw"></i></span>
						</div>
						<input type="password" class="form-control" placeholder="<?php echo lang('password');?>" id="password" name="password">
						<div class="invalid-feedback"></div>
					</div>
					
					<?php echo csrf_token_field(); ?>
					<div class="form-group">
						<button type="submit" class="btn btn-primary btn-block" id="submit" data-loading-text="<?php echo lang('submiting');?>..."><?php echo lang('login');?></button>
					</div>
					
					<div class="media">
						<div>
							
						</div>
						<div class="media-body text-right">
							
							<a href="<?php echo url('user-create');?>" class="text-muted"><small><?php echo lang('user_create');?></small></a>
							<?php if(!empty($conf['user_resetpw_on'])) { ?>
							<a href="<?php echo url('user-resetpw');?>" class="text-muted ml-3"><small><?php echo lang('forgot_pw');?></small></a>
							<?php } ?>
							
						</div>
					</div>
				</form>
			</div>
		</div>
		
	</div>
</div>



<?php include _include(APP_PATH.'view/htm/footer.inc.htm');?>

<script src="<?php echo $conf['view_url'];?>js/md5.js"></script>

<script>
var jform = $('#form');
var jsubmit = $('#submit');
var jemail = $('#email');
var jpassword = $('#password');
var referer = '<?php echo $referer;?>';
jform.on('submit', function() {
	jform.reset();
	jsubmit.button('loading');
	var postdata = jform.serializeObject();
	// 不再在前端进行MD5哈希，直接发送原始密码（通过HTTPS保护）
	$.xpost(jform.attr('action'), postdata, function(code, message) {
		if(code == 0) {
			jsubmit.button(message).delay(1000).location(referer);
		} else if(xn.is_number(code)) {
			alert(message);
			jsubmit.button('reset');
		} else {
			jform.find('[name="'+code+'"]').alert(message).focus();
			jsubmit.button('reset');
		}
		/*
		if(code == 0) {
			jsubmit.button(message).delay(1000).location(referer);
		} else if(code == 'email') {
			jemail.alert(message).focus();
			jsubmit.button('reset');
		} else if(code == 'password') {
			jpassword.alert(message).focus();
			jsubmit.button('reset');
		} else {
			alert(message);
			jsubmit.button('reset');
		}*/
	});
	return false;
});

</script>

