<?php
define('DEBUG', 1);
define('APP_PATH', __DIR__.'/');

// 包含必要的文件 - 使用正确的加载顺序
include APP_PATH.'index.php';

echo "<h2>管理后台调试</h2>\n";

echo "<h3>1. 函数检查</h3>\n";
echo "user_is_admin 函数存在: " . (function_exists('user_is_admin') ? '✅' : '❌') . "<br>\n";
echo "admin_token_check 函数存在: " . (function_exists('admin_token_check') ? '✅' : '❌') . "<br>\n";

echo "<h3>2. 变量检查</h3>\n";
echo "当前用户 gid: " . (isset($gid) ? $gid : 'undefined') . "<br>\n";
echo "当前用户 uid: " . (isset($uid) ? $uid : 'undefined') . "<br>\n";

if (function_exists('user_is_admin') && isset($gid)) {
    echo "user_is_admin($gid): " . (user_is_admin($gid) ? '✅ true' : '❌ false') . "<br>\n";
}

echo "<h3>3. 用户信息</h3>\n";
if (isset($user) && !empty($user)) {
    echo "用户名: " . htmlspecialchars($user['username']) . "<br>\n";
    echo "用户组: " . $user['gid'] . "<br>\n";
} else {
    echo "用户未登录<br>\n";
}

echo "<h3>4. 管理员组配置</h3>\n";
if (function_exists('user_is_admin')) {
    echo "gid=1 是管理员: " . (user_is_admin(1) ? '✅' : '❌') . "<br>\n";
    echo "gid=2 是管理员: " . (user_is_admin(2) ? '✅' : '❌') . "<br>\n";
}
?>
