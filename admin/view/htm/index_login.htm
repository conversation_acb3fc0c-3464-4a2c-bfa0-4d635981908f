<?php include _include(ADMIN_PATH.'view/htm/header.inc.htm');?>

<!--{hook admin_index_login_start.htm}-->
					
<div class="row">
	<div class="col-lg-5 mx-auto">
		<div class="card">
			<div class="card-body">
				<h4><?php echo lang('admin_login');?></h4>
				<div class="alert alert-info">
					<i class="icon-info-circle"></i>
					请先登录普通用户账号，然后在此输入密码进入管理后台
				</div>
				<form action="<?php echo url('index-login');?>" method="post" id="form">
					<div class="form-group"><?php echo lang('for_safe_input_password_again');?>：</div>
					<div class="form-group input-group">
						<div class="input-group-prepend">
							<span class="input-group-text"><i class="icon-lock"></i></span>
						</div>
						<input type="password" class="form-control" placeholder="<?php echo lang('password');?>" id="password" name="password">
					</div>
					<?php echo csrf_token_field(); ?>
					<div class="form-group text-right">
						<button type="submit" class="btn btn-primary btn-block" id="submit" data-loading-text="<?php echo lang('submiting');?>..."><?php echo lang('login');?></button>
					</div>
					<div class="text-center mt-3">
						<a href="../" class="text-muted">
							<i class="icon-arrow-left"></i> 返回网站首页
						</a>
						<span class="mx-2">|</span>
						<a href="../?user-login.htm" class="text-muted">
							<i class="icon-user"></i> 用户登录
						</a>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

<!--{hook admin_index_login_end.htm}-->

<?php include _include(ADMIN_PATH.'view/htm/footer.inc.htm');?>
<script src="../view/js/md5.js"></script>

<script>
var jform = $('#form');
var jsubmit = $('#submit');
var jemail = $('#email');
var jpassword = $('#password').focus();
jform.on('submit', function() {
	jform.reset();
	jsubmit.button('loading');
	var postdata = jform.serializeObject();
	// 不再在前端进行MD5哈希，直接发送原始密码（通过HTTPS保护）
	$.xpost(jform.attr('action'), postdata, function(code, message) {
		if(code == 0) {
			jsubmit.button(message).delay(1000).location('./');
		} else if(code == 'email') {
			jemail.alert(message).focus();
			jsubmit.button('reset');
		} else if(code == 'password') {
			jpassword.alert(message).focus();
			jsubmit.button('reset');
		} else {
			alert(message);
			jsubmit.button('reset');
		}
	});
	return false;
});

</script>

<!--{hook admin_index_login_js.htm}-->
