<?php
define('SKIP_ROUTE', TRUE);
include './index.php';

echo "<h2>调试模式检查</h2>\n";
echo "DEBUG 常量值: " . DEBUG . "<br>\n";
echo "是否调试模式: " . (DEBUG ? '✅ 是' : '❌ 否') . "<br>\n";

echo "<h3>模型文件加载方式</h3>\n";
if (DEBUG) {
    echo "✅ 使用调试模式 - 直接加载模型文件<br>\n";
} else {
    echo "❌ 使用生产模式 - 使用合并的模型文件<br>\n";
    $model_min_file = $conf['tmp_path'].'model.min.php';
    echo "合并文件路径: " . $model_min_file . "<br>\n";
    echo "合并文件存在: " . (is_file($model_min_file) ? '✅ 是' : '❌ 否') . "<br>\n";
    
    if (is_file($model_min_file)) {
        $file_size = filesize($model_min_file);
        echo "合并文件大小: " . number_format($file_size) . " bytes<br>\n";
        echo "合并文件修改时间: " . date('Y-m-d H:i:s', filemtime($model_min_file)) . "<br>\n";
    }
}

echo "<h3>检查函数定义</h3>\n";
$functions_to_check = array(
    'user_is_admin',
    'user_is_moderator', 
    'user_has_permission',
    'user_can_access_forum',
    'user_status_check',
    'is_password_strong'
);

foreach ($functions_to_check as $func) {
    echo "$func: " . (function_exists($func) ? '✅ 存在' : '❌ 不存在') . "<br>\n";
}

echo "<h3>建议</h3>\n";
if (!DEBUG) {
    echo "❗ 建议删除合并文件以强制重新生成：<br>\n";
    echo "<code>rm " . $model_min_file . "</code><br>\n";
}
?>
