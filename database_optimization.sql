-- Xiuno BBS 数据库优化脚本
-- 添加必要的索引以提高查询性能

USE xiuno_bbs;

-- 1. 用户表优化
-- 添加登录相关索引（如果不存在）
ALTER TABLE bbs_user ADD INDEX IF NOT EXISTS idx_login_date (login_date);
ALTER TABLE bbs_user ADD INDEX IF NOT EXISTS idx_create_date (create_date);
ALTER TABLE bbs_user ADD INDEX IF NOT EXISTS idx_mobile (mobile);

-- 2. 主题表优化
-- 添加复合索引优化列表查询
ALTER TABLE bbs_thread ADD INDEX IF NOT EXISTS idx_fid_last_date (fid, last_date);
ALTER TABLE bbs_thread ADD INDEX IF NOT EXISTS idx_fid_create_date (fid, create_date);
ALTER TABLE bbs_thread ADD INDEX IF NOT EXISTS idx_uid_create_date (uid, create_date);
ALTER TABLE bbs_thread ADD INDEX IF NOT EXISTS idx_top_last_date (top, last_date);

-- 添加搜索相关索引
ALTER TABLE bbs_thread ADD INDEX IF NOT EXISTS idx_subject (subject(32));

-- 3. 帖子表优化
-- 添加复合索引优化帖子查询
ALTER TABLE bbs_post ADD INDEX IF NOT EXISTS idx_tid_create_date (tid, create_date);
ALTER TABLE bbs_post ADD INDEX IF NOT EXISTS idx_uid_create_date (uid, create_date);
ALTER TABLE bbs_post ADD INDEX IF NOT EXISTS idx_isfirst (isfirst);

-- 4. 会话表优化（检查表是否存在）
-- ALTER TABLE bbs_session ADD INDEX IF NOT EXISTS idx_uid (uid);
-- ALTER TABLE bbs_session ADD INDEX IF NOT EXISTS idx_last_date (last_date);

-- 5. 论坛访问权限表优化
ALTER TABLE bbs_forum_access ADD INDEX IF NOT EXISTS idx_fid_gid (fid, gid);

-- 6. 我的主题表优化
ALTER TABLE bbs_mythread ADD INDEX IF NOT EXISTS idx_uid_tid (uid, tid);

-- 7. 我的帖子表优化
ALTER TABLE bbs_mypost ADD INDEX IF NOT EXISTS idx_uid_pid (uid, pid);

-- 12. 优化表结构
-- 将 MyISAM 表转换为 InnoDB（更好的事务支持和并发性能）
-- 注意：这会锁表，在生产环境中需要在维护窗口执行

-- ALTER TABLE bbs_user ENGINE=InnoDB;
-- ALTER TABLE bbs_thread ENGINE=InnoDB;
-- ALTER TABLE bbs_post ENGINE=InnoDB;
-- ALTER TABLE bbs_forum ENGINE=InnoDB;
-- ALTER TABLE bbs_attach ENGINE=InnoDB;

-- 13. 分析表以更新统计信息
ANALYZE TABLE bbs_user;
ANALYZE TABLE bbs_thread;
ANALYZE TABLE bbs_post;
ANALYZE TABLE bbs_forum;
ANALYZE TABLE bbs_attach;

-- 14. 优化表
OPTIMIZE TABLE bbs_user;
OPTIMIZE TABLE bbs_thread;
OPTIMIZE TABLE bbs_post;
OPTIMIZE TABLE bbs_forum;
OPTIMIZE TABLE bbs_attach;
