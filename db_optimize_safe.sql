-- Xiuno BBS 数据库优化脚本（安全版本）
-- 只添加不存在的索引

USE xiuno_bbs;

-- 检查并添加用户表索引
-- 登录日期索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = 'xiuno_bbs' 
     AND table_name = 'bbs_user' 
     AND index_name = 'idx_login_date') > 0,
    'SELECT "Index idx_login_date already exists"',
    'ALTER TABLE bbs_user ADD INDEX idx_login_date (login_date)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建日期索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = 'xiuno_bbs' 
     AND table_name = 'bbs_user' 
     AND index_name = 'idx_create_date') > 0,
    'SELECT "Index idx_create_date already exists"',
    'ALTER TABLE bbs_user ADD INDEX idx_create_date (create_date)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 手机号索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = 'xiuno_bbs' 
     AND table_name = 'bbs_user' 
     AND index_name = 'idx_mobile') > 0,
    'SELECT "Index idx_mobile already exists"',
    'ALTER TABLE bbs_user ADD INDEX idx_mobile (mobile)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 主题表复合索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = 'xiuno_bbs' 
     AND table_name = 'bbs_thread' 
     AND index_name = 'idx_fid_last_date') > 0,
    'SELECT "Index idx_fid_last_date already exists"',
    'ALTER TABLE bbs_thread ADD INDEX idx_fid_last_date (fid, last_date)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 主题表用户创建日期索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = 'xiuno_bbs' 
     AND table_name = 'bbs_thread' 
     AND index_name = 'idx_uid_create_date') > 0,
    'SELECT "Index idx_uid_create_date already exists"',
    'ALTER TABLE bbs_thread ADD INDEX idx_uid_create_date (uid, create_date)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 帖子表复合索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = 'xiuno_bbs' 
     AND table_name = 'bbs_post' 
     AND index_name = 'idx_tid_create_date') > 0,
    'SELECT "Index idx_tid_create_date already exists"',
    'ALTER TABLE bbs_post ADD INDEX idx_tid_create_date (tid, create_date)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 帖子表用户创建日期索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = 'xiuno_bbs' 
     AND table_name = 'bbs_post' 
     AND index_name = 'idx_uid_create_date') > 0,
    'SELECT "Index idx_uid_create_date already exists"',
    'ALTER TABLE bbs_post ADD INDEX idx_uid_create_date (uid, create_date)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 分析和优化表
ANALYZE TABLE bbs_user, bbs_thread, bbs_post, bbs_forum, bbs_attach;
OPTIMIZE TABLE bbs_user, bbs_thread, bbs_post, bbs_forum, bbs_attach;
