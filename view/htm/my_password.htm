<template include="./view/htm/my.template.htm">
	<slot name="my_body">
		<div class="col-lg-6 mx-auto">
			<form action="<?php echo url('my-password');?>" method="post" id="form">
				<div class="form-group input-group">
					<div class="input-group-prepend">
						<span class="input-group-text"><i class="icon-lock"></i></span>
					</div>
					<input type="password" class="form-control" id="password_old" name="password_old" placeholder="<?php echo lang('password_old');?>" required="">
					<div class="invalid-feedback"></div>
				</div>
				
				<div class="form-group input-group">
					<div class="input-group-prepend">
						<span class="input-group-text"><i class="icon-lock"></i></span>
					</div>
					<input type="password" class="form-control" id="password_new" name="password_new" placeholder="<?php echo lang('password_new');?>" required="">
					<div class="invalid-feedback"></div>
				</div>
				
				<div class="form-group input-group">
					<div class="input-group-prepend">
						<span class="input-group-text"><i class="icon-lock"></i></span>
					</div>
					<input type="password" class="form-control" id="password_new_repeat" name="password_new_repeat" placeholder="<?php echo lang('password_new_repeat');?>" required="">
					<div class="invalid-feedback"></div>
				</div>
				<?php echo csrf_token_field(); ?>
				<div>
					<button type="submit" class="btn btn-primary btn-block" id="submit" data-loading-text="<?php echo lang('submiting');?>..."><?php echo lang('update_password');?></button>
				</div>
			</form>
		</div>
	</slot>
</template>

<script src="<?php echo $conf['view_url'];?>js/md5.js"></script>

<script>
$('a[data-active="my"]').addClass('active');
$('a[data-active="my-password"]').addClass('active');
</script>

<script>
var jform = $('#form');
var jsubmit = $('#submit');
jform.on('submit', function() {
	jform.reset();
	jsubmit.button('loading');
	var postdata = jform.serializeObject();
	postdata.password_old = $.md5(postdata.password_old);
	postdata.password_new = $.md5(postdata.password_new);
	postdata.password_new_repeat = $.md5(postdata.password_new_repeat);
	$.xpost(jform.attr('action'), postdata, function(code, message) {
		if(code == 0) {
			$.alert(message);
			jsubmit.button(message).delay(3000).button('reset');
		} else if(xn.is_number(code)) {
			alert(message);
			jsubmit.button('reset');
		} else {
			jform.find('[name="'+code+'"]').alert(message).focus();
			jsubmit.button('reset');
		}
	});
	return false;
});
</script>